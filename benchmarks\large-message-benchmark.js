import crypto from 'crypto';
import EncryptionUtils from '../src/encryption/EncryptionUtils.js';

/**
 * 100KB大消息性能评估
 */
async function evaluate100KBMessage() {
    console.log('📦 100KB 大消息推送性能评估\n');
    
    const encryptionUtils = new EncryptionUtils();
    const messageSize = 100 * 1024; // 100KB
    
    console.log(`消息大小: ${messageSize.toLocaleString()} bytes (${(messageSize/1024).toFixed(1)} KB)`);
    console.log('=' .repeat(60));
    
    // 生成100KB测试数据 (模拟protobuf序列化后的数据)
    const testData = crypto.randomBytes(messageSize);
    console.log('✅ 测试数据生成完成\n');
    
    // 1. 单次加密/解密性能
    console.log('🔐 单次加密/解密性能测试:');
    
    const encryptStart = process.hrtime.bigint();
    const encrypted = encryptionUtils.encrypt(testData);
    const encryptTime = Number(process.hrtime.bigint() - encryptStart) / 1000000;
    
    const decryptStart = process.hrtime.bigint();
    const decrypted = encryptionUtils.decrypt(encrypted);
    const decryptTime = Number(process.hrtime.bigint() - decryptStart) / 1000000;
    
    // 验证数据完整性
    const isValid = testData.equals(decrypted);
    
    console.log(`  加密时间: ${encryptTime.toFixed(2)} ms`);
    console.log(`  解密时间: ${decryptTime.toFixed(2)} ms`);
    console.log(`  总处理时间: ${(encryptTime + decryptTime).toFixed(2)} ms`);
    console.log(`  数据完整性: ${isValid ? '✅ 验证通过' : '❌ 验证失败'}`);
    console.log(`  加密后大小: ${encrypted.length.toLocaleString()} bytes (+${encrypted.length - messageSize} bytes)`);
    console.log(`  数据膨胀率: +${((encrypted.length - messageSize) / messageSize * 100).toFixed(3)}%\n`);
    
    // 2. 吞吐量计算
    console.log('📊 吞吐量分析:');
    const encryptThroughput = (messageSize / 1024 / 1024) / (encryptTime / 1000);
    const decryptThroughput = (messageSize / 1024 / 1024) / (decryptTime / 1000);
    
    console.log(`  加密吞吐量: ${encryptThroughput.toFixed(1)} MB/s`);
    console.log(`  解密吞吐量: ${decryptThroughput.toFixed(1)} MB/s\n`);
    
    // 3. 批量处理性能 (模拟多客户端)
    console.log('👥 多客户端并发推送模拟:');
    const clientCounts = [1, 5, 10, 20, 50];
    
    for (const clientCount of clientCounts) {
        const batchStart = process.hrtime.bigint();
        const encryptedMessages = [];
        
        // 为每个客户端加密消息
        for (let i = 0; i < clientCount; i++) {
            encryptedMessages.push(encryptionUtils.encrypt(testData));
        }
        
        const batchTime = Number(process.hrtime.bigint() - batchStart) / 1000000;
        const totalDataMB = (messageSize * clientCount) / 1024 / 1024;
        const batchThroughput = totalDataMB / (batchTime / 1000);
        
        console.log(`  ${clientCount.toString().padStart(2)} 客户端: ${batchTime.toFixed(1)}ms, ${batchThroughput.toFixed(1)} MB/s, 平均 ${(batchTime/clientCount).toFixed(2)}ms/客户端`);
    }
    
    // 4. 内存使用分析
    console.log('\n💾 内存使用分析:');
    const initialMemory = process.memoryUsage();
    
    // 模拟缓存100个100KB消息 (10MB数据)
    const messageCache = [];
    for (let i = 0; i < 100; i++) {
        messageCache.push(encryptionUtils.encrypt(testData));
    }
    
    const afterMemory = process.memoryUsage();
    const memoryIncrease = (afterMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024;
    
    console.log(`  缓存100条100KB消息:`);
    console.log(`  内存增长: ${memoryIncrease.toFixed(2)} MB`);
    console.log(`  理论数据大小: ${(100 * messageSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`  内存效率: ${((100 * messageSize / 1024 / 1024) / memoryIncrease * 100).toFixed(1)}%`);
    
    // 5. 网络传输时间估算
    console.log('\n🌐 网络传输时间估算:');
    const networkScenarios = [
        { name: '千兆局域网', bandwidth: 1000 }, // Mbps
        { name: '百兆局域网', bandwidth: 100 },
        { name: '4G网络', bandwidth: 50 },
        { name: '宽带网络', bandwidth: 20 },
        { name: 'WiFi网络', bandwidth: 10 }
    ];
    
    for (const scenario of networkScenarios) {
        const transferTime = (encrypted.length * 8) / (scenario.bandwidth * 1000000) * 1000; // ms
        console.log(`  ${scenario.name}: ${transferTime.toFixed(1)} ms`);
    }
    
    // 6. 实际应用场景分析
    console.log('\n🎯 实际应用场景分析:');
    
    console.log('📈 高频推送场景 (每秒1次100KB):');
    const highFreqCpuTime = encryptTime * 1; // 1次/秒
    const highFreqCpuPercent = (highFreqCpuTime / 1000) * 100;
    console.log(`  CPU时间: ${highFreqCpuTime.toFixed(2)} ms/s = ${highFreqCpuPercent.toFixed(3)}% CPU`);
    console.log(`  内存需求: ~${(encrypted.length / 1024).toFixed(1)} KB/消息`);
    
    console.log('\n📊 中频推送场景 (每10秒1次100KB):');
    const medFreqCpuTime = encryptTime * 0.1; // 0.1次/秒
    const medFreqCpuPercent = (medFreqCpuTime / 1000) * 100;
    console.log(`  CPU时间: ${medFreqCpuTime.toFixed(3)} ms/s = ${medFreqCpuPercent.toFixed(4)}% CPU`);
    
    console.log('\n📉 低频推送场景 (每分钟1次100KB):');
    const lowFreqCpuTime = encryptTime / 60; // 1/60次/秒
    const lowFreqCpuPercent = (lowFreqCpuTime / 1000) * 100;
    console.log(`  CPU时间: ${lowFreqCpuTime.toFixed(4)} ms/s = ${lowFreqCpuPercent.toFixed(5)}% CPU`);
    
    // 7. 与小消息对比
    console.log('\n📋 与小消息(1KB)对比:');
    const smallMsgData = crypto.randomBytes(1024);
    
    const smallEncryptStart = process.hrtime.bigint();
    const smallEncrypted = encryptionUtils.encrypt(smallMsgData);
    const smallEncryptTime = Number(process.hrtime.bigint() - smallEncryptStart) / 1000000;
    
    console.log(`  1KB消息加密时间: ${smallEncryptTime.toFixed(3)} ms`);
    console.log(`  100KB消息加密时间: ${encryptTime.toFixed(2)} ms`);
    console.log(`  时间比例: ${(encryptTime / smallEncryptTime).toFixed(1)}:1`);
    console.log(`  效率提升: 100KB消息的单位时间效率是1KB的 ${(100 / (encryptTime / smallEncryptTime)).toFixed(1)} 倍`);
    
    // 8. 推荐配置
    console.log('\n⚙️  推荐配置:');
    console.log('🔧 服务器配置:');
    console.log('  • CPU: 4核心以上 (支持并发加密)');
    console.log('  • 内存: 8GB以上 (缓存大消息)');
    console.log('  • 网络: 千兆网卡 (快速传输)');
    
    console.log('\n🔧 应用配置:');
    console.log('  • 消息队列: 限制并发加密数量');
    console.log('  • 内存管理: 及时释放已发送消息');
    console.log('  • 错误处理: 大消息传输失败重试机制');
    
    // 9. 性能瓶颈分析
    console.log('\n⚠️  性能瓶颈分析:');
    if (encryptTime > 10) {
        console.log('❌ 加密时间过长，可能影响实时性');
    } else if (encryptTime > 5) {
        console.log('⚠️  加密时间较长，建议优化');
    } else {
        console.log('✅ 加密时间合理');
    }
    
    if (memoryIncrease > 50) {
        console.log('❌ 内存使用过高，需要优化缓存策略');
    } else if (memoryIncrease > 20) {
        console.log('⚠️  内存使用较高，建议监控');
    } else {
        console.log('✅ 内存使用合理');
    }
    
    // 10. 最终建议
    console.log('\n🎯 最终建议:');
    console.log('✅ 100KB消息加密完全可行');
    console.log('✅ 性能开销在可接受范围内');
    console.log('✅ 适合实时数据推送场景');
    
    if (encryptTime < 2) {
        console.log('🚀 推荐: 可以放心使用加密传输');
    } else if (encryptTime < 5) {
        console.log('⚡ 推荐: 适合中低频推送场景');
    } else {
        console.log('🐌 建议: 考虑消息分片或压缩');
    }
}

// 运行评估
evaluate100KBMessage().catch(console.error);
